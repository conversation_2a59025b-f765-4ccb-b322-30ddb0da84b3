#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "turtlesim/msg/pose.hpp"
#include <cmath>

class SFMNode : public rclcpp::Node
{
public:
    SFMNode() : Node("sfm_node")
    {
        cmd_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("/turtle1/cmd_vel", 10);
        pose_sub_ = this->create_subscription<turtlesim::msg::Pose>(
            "/turtle1/pose", 10,
            std::bind(&SFMNode::pose_callback, this, std::placeholders::_1));

        goal_x_ = 8.0;  // target X
        goal_y_ = 8.0;  // target Y
        gain_ = 2.0;    // percepatan menuju target
    }

private:
    void pose_callback(const turtlesim::msg::Pose::SharedPtr msg)
    {
        double dx = goal_x_ - msg->x;
        double dy = goal_y_ - msg->y;
        double distance = std::hypot(dx, dy);
        double angle_to_goal = std::atan2(dy, dx);
        double angle_diff = angle_to_goal - msg->theta;

        geometry_msgs::msg::Twist cmd;

        if (distance > 0.1) {
            cmd.angular.z = 4.0 * angle_diff;
            if (std::abs(angle_diff) < 0.1)
                cmd.linear.x = gain_ * distance;
        }

        cmd_pub_->publish(cmd);
    }

    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_pub_;
    rclcpp::Subscription<turtlesim::msg::Pose>::SharedPtr pose_sub_;

    double goal_x_, goal_y_, gain_;
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<SFMNode>());
    rclcpp::shutdown();
    return 0;
}
