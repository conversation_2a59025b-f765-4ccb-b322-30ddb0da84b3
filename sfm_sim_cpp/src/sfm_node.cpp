#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "turtlesim/msg/pose.hpp"
#include "turtlesim/srv/teleport_absolute.hpp"
#include <cmath>

class SFMNode : public rclcpp::Node
{
public:
    SFMNode() : Node("sfm_node")
    {
        cmd_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("/turtle1/cmd_vel", 10);

        pose_sub_ = this->create_subscription<turtlesim::msg::Pose>(
            "/turtle1/pose", 10,
            std::bind(&SFMNode::turtle1_callback, this, std::placeholders::_1));

        obs_sub_ = this->create_subscription<turtlesim::msg::Pose>(
            "/turtle2/pose", 10,
            std::bind(&SFMNode::obstacle_callback, this, std::placeholders::_1));

        teleport_cli_ = this->create_client<turtlesim::srv::TeleportAbsolute>("/turtle1/teleport_absolute");

        // Default goal dan start
        goal_x_ = 8.0;
        goal_y_ = 8.0;
        start_x_ = 2.0;
        start_y_ = 2.0;

        resetting_ = false;
    }

private:
    turtlesim::msg::Pose::SharedPtr self_pose_;
    turtlesim::msg::Pose::SharedPtr obs_pose_;
    bool resetting_;

    double goal_x_, goal_y_;
    double start_x_, start_y_;

    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_pub_;
    rclcpp::Subscription<turtlesim::msg::Pose>::SharedPtr pose_sub_, obs_sub_;
    rclcpp::Client<turtlesim::srv::TeleportAbsolute>::SharedPtr teleport_cli_;

    void turtle1_callback(const turtlesim::msg::Pose::SharedPtr msg)
    {
        self_pose_ = msg;
        update_motion();
    }

    void obstacle_callback(const turtlesim::msg::Pose::SharedPtr msg)
    {
        obs_pose_ = msg;
        update_motion();
    }

    void update_motion()
    {
        if (!self_pose_ || !obs_pose_ || resetting_) return;

        double fx = 0.0;
        double fy = 0.0;

        // Gaya tarik
        double dx = goal_x_ - self_pose_->x;
        double dy = goal_y_ - self_pose_->y;
        double dist_to_goal = std::hypot(dx, dy);

        fx += 1.5 * dx;
        fy += 1.5 * dy;

        // Gaya tolak
        double rx = self_pose_->x - obs_pose_->x;
        double ry = self_pose_->y - obs_pose_->y;
        double dist_obs = std::hypot(rx, ry);

        if (dist_obs < 2.0)
        {
            double rep_gain = 5.0 / (dist_obs * dist_obs + 0.1);
            fx += rep_gain * rx;
            fy += rep_gain * ry;
        }

        // Sudut arah hasil gaya
        double desired_theta = std::atan2(fy, fx);
        double angle_diff = desired_theta - self_pose_->theta;

        while (angle_diff > M_PI) angle_diff -= 2 * M_PI;
        while (angle_diff < -M_PI) angle_diff += 2 * M_PI;

        geometry_msgs::msg::Twist cmd;

        if (dist_to_goal > 0.1)
        {
            cmd.angular.z = 4.0 * angle_diff;
            if (std::abs(angle_diff) < 0.2)
                cmd.linear.x = 2.0 * std::hypot(fx, fy);
        }
        else
        {
            // Reset ke awal kalau sudah dekat goal
            cmd.linear.x = 0.0;
            cmd.angular.z = 0.0;
            cmd_pub_->publish(cmd);

            resetting_ = true;
            RCLCPP_INFO(this->get_logger(), "Goal reached. Resetting position...");

            // Delay 1 detik sebelum reset
            rclcpp::sleep_for(std::chrono::milliseconds(1000));
            teleport_to_start();
            return;
        }

        cmd_pub_->publish(cmd);
    }

    void teleport_to_start()
    {
        if (!teleport_cli_->wait_for_service(std::chrono::seconds(1)))
        {
            RCLCPP_WARN(this->get_logger(), "Teleport service not available.");
            resetting_ = false;
            return;
        }

        auto req = std::make_shared<turtlesim::srv::TeleportAbsolute::Request>();
        req->x = start_x_;
        req->y = start_y_;
        req->theta = 0.0;

        auto future = teleport_cli_->async_send_request(req);
        future.wait();

        RCLCPP_INFO(this->get_logger(), "Turtle reset. Starting again.");
        resetting_ = false;
    }
};
